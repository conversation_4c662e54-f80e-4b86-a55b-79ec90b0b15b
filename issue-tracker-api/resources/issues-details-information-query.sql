-- Issue Info Statement # 1
SELECT DISTINCT ON (issue)
	issue_type,
    issue,
    issue_summary,
    assignee_name,
    status,
    issue_priority,
    issue_priority_icon
FROM
    events
where source ='jira'
ORDER BY
issue, created_at desc;

-- Timeline Events Statement
SELECT
    event_type
    , summarize_minutes (calculate_working_minutes (LAG(created_at , 1) OVER (PARTITION BY issue ORDER BY created_at) , created_at)) AS elapsed_time
    , source
    , actor_name
    , created_at 
FROM
    events
WHERE
    issue = 'GPS-5145'
ORDER BY
    created_at desc;

-- Progress bar Statement    
WITH status_periods AS (
  SELECT
    issue,
    status,
    created_at AS started_at,
    -- Get the timestamp of the next status change for the same issue
    COALESCE(
      LEAD(created_at, 1) OVER (PARTITION BY issue ORDER BY created_at),
      NOW() -- For the current status, assume it ends now
    ) AS ended_at
  FROM
    events
  WHERE
    -- Ensure we only consider records that represent a status
    status IS NOT NULL AND status <> '' AND issue = 'GPS-5060' and "source" = 'jira'
)
SELECT
  issue,
  status,
  summarize_minutes(
    SUM(calculate_working_minutes(started_at, ended_at))
  ) AS time_in_status
FROM
  status_periods
GROUP BY
  issue, status
ORDER BY
  issue, MIN(started_at);
-- Statistics queries
-- Calculate average time by issue that Jira issues spend in "code review" status
WITH code_review_events AS (
    SELECT 
        issue,
        created_at,
        status,
        -- Get the timestamp of the next event for this issue
        LEAD(created_at) OVER (PARTITION BY issue ORDER BY created_at) as next_event_time
    FROM events 
    WHERE source = 'jira'
        AND LOWER(status) = 'code review'
),
code_review_periods AS (
    SELECT 
        issue,
        created_at as entered_code_review,
        next_event_time,
        -- Calculate working minutes between this event and the next event (or current time)
        calculate_working_minutes(
            created_at, 
            COALESCE(next_event_time, NOW())
        ) as minutes_in_period
    FROM code_review_events
),
total_time_per_issue AS (
    SELECT 
        issue,
        SUM(minutes_in_period) as total_minutes_in_code_review,
        COUNT(*) as code_review_periods
    FROM code_review_periods
    GROUP BY issue
)
SELECT 
    COUNT(*) as total_issues,
    summarize_minutes(ROUND(AVG(total_minutes_in_code_review), 2)) as "value",
    summarize_minutes(2880) as target_time,
    "avg_time_for_code_review" as stat_type
FROM total_time_per_issue;E minutes_in_code_review IS NOT NULL;

-- Calculate average time for Bug issues to go from "For QA" to "To Do" status
WITH bug_status_events AS (
    SELECT 
        issue,
        created_at,
        LOWER(status) as status_lower
    FROM events 
    WHERE source = 'jira'
        AND status IS NOT NULL
        AND LOWER(issue_type) = 'bug'
    ORDER BY issue, created_at
),
qa_events AS (
    SELECT 
        issue,
        created_at as qa_time
    FROM bug_status_events
    WHERE status_lower = 'for qa'
),
todo_events AS (
    SELECT 
        issue,
        created_at as todo_time
    FROM bug_status_events
    WHERE status_lower = 'to do'
),
issue_transitions AS (
    SELECT 
        q.issue,
        q.qa_time,
        -- Find the first "To Do" event after each "For QA" event
        (SELECT MIN(td.todo_time) 
         FROM todo_events td 
         WHERE td.issue = q.issue 
           AND td.todo_time > q.qa_time) as todo_time
    FROM qa_events q
),
transition_durations AS (
    SELECT 
        issue,
        qa_time,
        todo_time,
        -- Calculate working minutes from "For QA" to "To Do"
        CASE 
            WHEN todo_time IS NOT NULL THEN 
                calculate_working_minutes(qa_time, todo_time)
            ELSE NULL -- Issue never went back to "To Do" status
        END as minutes_qa_to_todo
    FROM issue_transitions
    WHERE todo_time IS NOT NULL -- Only include completed transitions
),
avg_time_per_issue AS (
    SELECT 
        issue,
        AVG(minutes_qa_to_todo) as avg_minutes_per_issue,
        COUNT(*) as transitions_count
    FROM transition_durations
    GROUP BY issue
)
SELECT 
    COUNT(*) as total_bug_issues,
    summarize_minutes(ROUND(AVG(avg_minutes_per_issue), 2)) as "value",
    summarize_minutes(1440) as target_time,
    "avg_time_for_bug_qa_to_todo" as stat_type
FROM avg_time_per_issue;

-- Calculate average time Jira issues stay in "For Testing" status
WITH for_testing_events AS (
    SELECT 
        issue,
        created_at,
        status,
        -- Get the timestamp of the next event for this issue
        LEAD(created_at) OVER (PARTITION BY issue ORDER BY created_at) as next_event_time
    FROM events 
    WHERE source = 'jira'
        AND LOWER(status) = 'for testing'
),
testing_periods AS (
    SELECT 
        issue,
        created_at as entered_testing,
        next_event_time,
        -- Calculate working minutes between this event and the next event (or current time)
        calculate_working_minutes(
            created_at, 
            COALESCE(next_event_time, NOW())
        ) as minutes_in_period
    FROM for_testing_events
),
total_time_per_issue AS (
    SELECT 
        issue,
        SUM(minutes_in_period) as total_minutes_in_testing,
        COUNT(*) as testing_periods
    FROM testing_periods
    GROUP BY issue
)
SELECT 
    COUNT(*) as total_issues,
    summarize_minutes(ROUND(AVG(total_minutes_in_testing), 2)) as as "value",
    summarize_minutes(1440) as target_time,
    "avg_time_for_testing" as stat_type
FROM total_time_per_issue;