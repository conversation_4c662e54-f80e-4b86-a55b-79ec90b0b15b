import { IsDateString, IsOptional, Validate } from 'class-validator';
import { ValidatorConstraint, ValidatorConstraintInterface } from 'class-validator';

@ValidatorConstraint({ name: 'DateRangeOrder', async: false })
class DateRangeOrder implements ValidatorConstraintInterface {
  validate(_: any, obj: any) {
    if (obj.startDate && obj.endDate) {
      return new Date(obj.startDate).getTime() <= new Date(obj.endDate).getTime();
    }
    return true;
  }
  defaultMessage() {
    return 'startDate must be before or equal to endDate';
  }
}

export class DateRangeQueryDto {
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;

  // Class-level validation to ensure proper ordering when both are provided
  @Validate(DateRangeOrder)
  private readonly _orderCheck?: unknown;
}

