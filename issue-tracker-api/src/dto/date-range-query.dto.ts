import {
    IsDateString,
    IsOptional,
    Validate,
    ValidatorConstraint,
    ValidatorConstraintInterface,
    ValidationArguments,
} from "class-validator";

interface DateRangeObject {
    startDate?: string;
    endDate?: string;
}

@ValidatorConstraint({ name: "DateRangeOrder", async: false })
class DateRangeOrder implements ValidatorConstraintInterface {
    validate(value: unknown, args: ValidationArguments): boolean {
        const obj = args.object as DateRangeObject;
        if (obj.startDate && obj.endDate) {
            return (
                new Date(obj.startDate).getTime() <=
                new Date(obj.endDate).getTime()
            );
        }
        return true;
    }

    defaultMessage(): string {
        return "startDate must be before or equal to endDate";
    }
}

export class DateRangeQueryDto {
    @IsOptional()
    @IsDateString()
    startDate?: string;

    @IsOptional()
    @IsDateString()
    endDate?: string;

    // Class-level validation to ensure proper ordering when both are provided
    @Validate(DateRangeOrder)
    private readonly _orderCheck?: unknown;
}
