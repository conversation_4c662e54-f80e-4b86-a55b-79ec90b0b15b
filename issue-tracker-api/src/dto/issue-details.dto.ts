export class IssueDetailsDto {
    issue_key: string;
    issue_type: string;
    issue_summary: string;
    assignee_name: string;
    current_status: string;
    issue_priority: string;
    issue_priority_icon: string;
}

export class ChangeMessageDto {
    field: string;
    from: string | null;
    to: string | null;
    actor: string;
    msg_template: string;
}

export class TimelineEventDto {
    event_type: string;
    elapsed_time: string | null;
    source: string;
    actor_name: string;
    repository: string;
    pr_title: string | null;
    pr_link: string | null;
    pr_id: string | null;
    pr_is_draft: boolean | null;
    is_pr_branch_closed: boolean | null;
    issue_changelog: any;
    created_at: string;
}

export class StatusProgressDto {
    status: string;
    time_in_status: string;
}

export class StatisticDto {
    total_issues: number;
    value: string;
    target_time: string;
    stat_type: string;
}

export class GroupedStatisticsDto {
    dev: StatisticDto[];
    qa: StatisticDto[];
}

export class FiltersDto {
    actors: string[];
    events: string[];
}

export class IssueDetailsResponseDto {
    issue_details: IssueDetailsDto;
    timeline: TimelineEventDto[];
    status_progress: StatusProgressDto[];
    filters: FiltersDto;
}
