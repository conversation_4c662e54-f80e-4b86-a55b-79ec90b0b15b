import { Controller, Get, Param, Query } from "@nestjs/common";
import { DatabaseService } from "../services/database.service";
import {
    IssueListItemDto,
    IssueDetailsResponseDto,
    GroupedStatisticsDto,
    DateRangeQueryDto,
} from "../dto";

@Controller("issues")
export class IssuesController {
    constructor(private readonly databaseService: DatabaseService) {}

    @Get()
    async getIssuesList(
        @Query() q: DateRangeQueryDto
    ): Promise<IssueListItemDto[]> {
        const start = q.startDate ? new Date(q.startDate) : null;
        const end = q.endDate ? new Date(q.endDate) : null;
        return await this.databaseService.getIssuesList(start, end);
    }

    @Get("statistics")
    async getStatistics(
        @Query() q: DateRangeQueryDto
    ): Promise<GroupedStatisticsDto> {
        const start = q.startDate ? new Date(q.startDate) : null;
        const end = q.endDate ? new Date(q.endDate) : null;
        return await this.databaseService.getStatistics(start, end);
    }

    @Get(":issueKey")
    async getIssueDetails(
        @Param("issueKey") issueKey: string
    ): Promise<IssueDetailsResponseDto> {
        return await this.databaseService.getIssueDetails(issueKey);
    }
}
