import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    inject,
    OnInit,
    signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { formatDuration, startOfMonth, endOfMonth } from 'date-fns';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { IssueService } from '../../issue';
import { ColorScaleAdvancedPipe } from '../../pipes/colour-scale.pipe';
import { DurationPipe } from '../../pipes/duration.pipe';
import { StatusBadgeStylePipe } from '../../pipes/status-badge-style.pipe';
import { GroupedStatistics } from '../../types';

@Component({
    selector: 'app-issues-list',
    imports: [
        CommonModule,
        FormsModule,
        StatusBadgeStylePipe,
        NzTableModule,
        NzTagModule,
        NzDatePickerModule,
        NzIconModule,
        NzButtonModule,
        ColorScaleAdvancedPipe,
        DurationPipe,
    ],
    templateUrl: './issues-list.html',
    styleUrl: './issues-list.css',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IssuesListComponent implements OnInit {
    private issueService = inject(IssueService);
    private router = inject(Router);

    searchQuery = signal('');
    dateRange = signal<[Date, Date] | null>(null);
    statistics = signal<GroupedStatistics>({ dev: [], qa: [] });

    filteredIssues = computed(() => {
        const query = this.searchQuery();
        const dateFilter = this.dateRange();
        return this.issueService.searchIssues(query, dateFilter);
    });

    avgTimeCodeReview = computed(() => {
        const devStats = this.statistics().dev;
        const avgTimeStat = devStats.find(
            (stat) => stat.stat_type === 'avg_time_for_code_review'
        );
        return avgTimeStat || null;
    });

    avgTimeBugQaToTodo = computed(() => {
        const qaStats = this.statistics().qa;
        const avgTimeStat = qaStats.find(
            (stat) => stat.stat_type === 'avg_time_for_bug_for_qa_to_todo'
        );
        return avgTimeStat || null;
    });

    avgTimeForTesting = computed(() => {
        const qaStats = this.statistics().qa;
        const avgTimeStat = qaStats.find(
            (stat) => stat.stat_type === 'avg_time_for_testing'
        );
        return avgTimeStat || null;
    });
    formatRelative_ = formatDuration;
    ngOnInit() {
        // Initialize with last 7 days
        const initialRange: [Date, Date] = [
            startOfMonth(new Date()),
            endOfMonth(new Date()),
        ];
        this.dateRange.set(initialRange);
        this.fetchDataForDateRange(initialRange);
    }

    onSearchChange() {
        this.searchQuery.set(
            (document.getElementById('issue-search') as HTMLInputElement)
                ?.value || ''
        );
    }

    onDateRangeChange(dates: [Date, Date] | null) {
        this.dateRange.set(dates);
        this.fetchDataForDateRange(dates);
    }

    clearDateFilter() {
        this.dateRange.set(null);
        this.fetchDataForDateRange(null);
    }

    private fetchDataForDateRange(dates: [Date, Date] | null) {
        const start = dates?.[0] ?? null;
        const end = dates?.[1] ?? null;

        this.issueService.loadIssues(start, end).subscribe((issues) => {
            this.issueService.updateIssuesSignal(issues);
        });

        this.issueService.getStatistics(start, end).subscribe((stats) => {
            this.statistics.set(stats);
        });
    }

    navigateToIssue(issueKey: string) {
        this.router.navigate(['/issues', issueKey]);
    }

    getStatusBadgeClass(): string {
        return 'inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-medium text-white';
    }
}
