<div class="flex min-h-screen flex-col">
    <header class="sticky top-0 z-10 bg-white shadow-sm">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div class="flex h-16 items-center justify-between">
                <div class="flex items-center gap-4">
                    <div class="flex-shrink-0">
                        <img src="logo-32x32.svg" alt="Logo" />
                    </div>
                    <h1 class="text-xl font-bold text-gray-900">
                        Issue Tracker
                    </h1>
                </div>
            </div>
        </div>
    </header>
    <main class="flex-1">
        <div class="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
            <div class="mx-auto max-w-6xl">
                <div class="text-center">
                    <h2
                        class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl"
                    >
                        Search Issues
                    </h2>
                    <p class="mt-4 text-lg text-gray-600">
                        Find issues by key, summary, or description.
                    </p>
                </div>
                <div class="mt-8">
                    <div class="relative">
                        <div
                            class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"
                        >
                            <svg
                                aria-hidden="true"
                                class="h-5 w-5 text-gray-400"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    clip-rule="evenodd"
                                    d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                                    fill-rule="evenodd"
                                ></path>
                            </svg>
                        </div>
                        <input
                            class="block w-full rounded-md border-0 py-3 pl-10 pr-3 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-blue-600 sm:text-sm sm:leading-6"
                            id="issue-search"
                            name="issue-search"
                            placeholder="Search for issues..."
                            type="search"
                            [(ngModel)]="searchQuery"
                            (input)="onSearchChange()"
                        />
                    </div>
                </div>
                <div class="mt-10 grid grid-cols-1 gap-10 lg:grid-cols-2">
                    <div class="space-y-6">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">
                            Development Team Statistics
                        </h3>
                        <div class="space-y-5">
                            <div
                                class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
                            >
                                <div>
                                    <dt
                                        class="truncate text-sm font-medium text-gray-500"
                                    >
                                        Average Working Time in Code Review
                                    </dt>
                                    @if (avgTimeCodeReview(); as stat) {
                                    <dd
                                        class="mt-1 flex items-baseline gap-x-2"
                                    >
                                        <span
                                            class="text-3xl font-semibold tracking-tight"
                                            [style.color]="
                                                +stat.value
                                                    | colorScaleAdvanced
                                                        : stat.target_time
                                            "
                                            >{{ +stat.value | duration }}</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >work hours</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >(Target: &lt;
                                            {{
                                                stat.target_time | duration
                                            }})</span
                                        >
                                    </dd>
                                    <dd class="mt-1 text-sm text-gray-500">
                                        Based on {{ stat.total_issues }} issues
                                    </dd>
                                    } @else {
                                    <dd
                                        class="mt-1 flex items-baseline gap-x-2"
                                    >
                                        <span
                                            class="text-3xl font-semibold tracking-tight text-gray-400"
                                            >--</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >(Loading...)</span
                                        >
                                    </dd>
                                    <dd class="mt-1 text-sm text-gray-500">
                                        Loading statistics...
                                    </dd>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="space-y-6">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">
                            QA Team Statistics
                        </h3>
                        <div class="space-y-5">
                            <div
                                class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
                            >
                                <div>
                                    <dt
                                        class="truncate text-sm font-medium text-gray-500"
                                    >
                                        Average Working Time in Testing
                                    </dt>
                                    @if (avgTimeForTesting(); as stat) {
                                    <dd
                                        class="mt-1 flex items-baseline gap-x-2"
                                    >
                                        <span
                                            class="text-3xl font-semibold tracking-tight"
                                            [style.color]="
                                                +stat.value
                                                    | colorScaleAdvanced
                                                        : stat.target_time
                                            "
                                            >{{ +stat.value | duration }}</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >work hours</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >(Target: &lt;
                                            {{
                                                stat.target_time | duration
                                            }})</span
                                        >
                                    </dd>
                                    <dd class="mt-1 text-sm text-gray-500">
                                        Based on {{ stat.total_issues }} issues
                                    </dd>
                                    } @else {
                                    <dd
                                        class="mt-1 flex items-baseline gap-x-2"
                                    >
                                        <span
                                            class="text-3xl font-semibold tracking-tight text-gray-400"
                                            >--</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >(Loading...)</span
                                        >
                                    </dd>
                                    <dd class="mt-1 text-sm text-gray-500">
                                        Loading statistics...
                                    </dd>
                                    }
                                </div>
                            </div>
                            <div
                                class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
                            >
                                <div>
                                    <dt
                                        class="truncate text-sm font-medium text-gray-500"
                                    >
                                        Average Working Time in "For QA"
                                    </dt>
                                    @if (avgTimeBugQaToTodo(); as stat) {
                                    <dd
                                        class="mt-1 flex items-baseline gap-x-2"
                                    >
                                        <span
                                            class="text-3xl font-semibold tracking-tight"
                                            [style.color]="
                                                +stat.value
                                                    | colorScaleAdvanced
                                                        : stat.target_time
                                            "
                                            >{{ +stat.value | duration }}</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >work hours</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >(Target: &lt;
                                            {{
                                                stat.target_time | duration
                                            }})</span
                                        >
                                    </dd>
                                    <dd class="mt-1 text-sm text-gray-500">
                                        Based on {{ stat.total_issues }} issues
                                    </dd>
                                    } @else {
                                    <dd
                                        class="mt-1 flex items-baseline gap-x-2"
                                    >
                                        <span
                                            class="text-3xl font-semibold tracking-tight text-gray-400"
                                            >--</span
                                        >
                                        <span class="text-sm text-gray-500"
                                            >(Loading...)</span
                                        >
                                    </dd>
                                    <dd class="mt-1 text-sm text-gray-500">
                                        Loading statistics...
                                    </dd>
                                    }
                                </div>
                            </div>
                            <!-- <div
                                class="overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6"
                            >
                                <dt
                                    class="truncate text-sm font-medium text-gray-500"
                                >
                                    Number of Reopened Issues
                                </dt>
                                <dd
                                    class="mt-1 text-3xl font-semibold tracking-tight text-gray-900"
                                >
                                    3
                                </dd>
                            </div> -->
                        </div>
                    </div>
                </div>
                <div class="mt-10">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-medium leading-6 text-gray-900">
                            Issues ({{ filteredIssues().length }})
                        </h3>
                        <div class="flex items-center gap-4">
                            <!-- Date Range Filter -->
                            <div class="flex items-center gap-2">
                                <nz-icon
                                    nzType="calendar"
                                    nzTheme="outline"
                                    class="text-lg text-gray-400"
                                />
                                <nz-range-picker
                                    [nzSize]="'default'"
                                    [nzAllowClear]="true"
                                    [nzPlaceHolder]="['Start date', 'End date']"
                                    [ngModel]="dateRange()"
                                    [nzSuffixIcon]="''"
                                    (ngModelChange)="onDateRangeChange($event)"
                                    class="w-64"
                                >
                                </nz-range-picker>
                            </div>
                            @if (dateRange()) {
                            <button
                                nz-button
                                nzType="default"
                                nzSize="default"
                                (click)="clearDateFilter()"
                                title="Clear date filter"
                            >
                                <nz-icon
                                    nzType="close"
                                    nzTheme="outline"
                                ></nz-icon>
                                Clear
                            </button>
                            }
                        </div>
                    </div>
                    <div class="mt-4">
                        <nz-table
                            #basicTable
                            [nzData]="filteredIssues()"
                            [nzPageSize]="10"
                            [nzShowPagination]="true"
                            [nzSize]="'middle'"
                            [nzBordered]="false"
                            class="ant-table-wrapper border border-gray-150"
                        >
                            <thead>
                                <tr>
                                    <th>Key</th>
                                    <th>Type</th>
                                    <th>Priority</th>
                                    <th>Summary</th>
                                    <th>Status</th>
                                    <th>Assignee</th>
                                    <th>Open PRs</th>
                                    <th>Updated</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr *ngFor="let issue of basicTable.data">
                                    <td>
                                        <a
                                            class="text-blue-600 hover:text-blue-700 cursor-pointer"
                                            (click)="
                                                navigateToIssue(issue.issue)
                                            "
                                            >{{ issue.issue }}</a
                                        >
                                    </td>
                                    <td>{{ issue.issue_type }}</td>
                                    <td>
                                        <div class="flex items-center gap-2">
                                            <img
                                                [src]="
                                                    issue.issue_priority_icon
                                                "
                                                [alt]="issue.issue_priority"
                                                class="w-4 h-4"
                                            />
                                            <span
                                                >{{ issue.issue_priority }}
                                            </span>
                                        </div>
                                    </td>
                                    <td class="max-w-xs">
                                        <div
                                            class="truncate"
                                            [title]="issue.issue_summary"
                                        >
                                            {{ issue.issue_summary }}
                                        </div>
                                    </td>
                                    <td>
                                        <nz-tag
                                            [nzColor]="(issue.status | statusBadgeStyle)?.['backgroundColor']"
                                            class="rounded-md"
                                        >
                                            {{ issue.status }}
                                        </nz-tag>
                                    </td>
                                    <td>{{ issue.assignee_name }}</td>
                                    <td>{{ issue.prs }}</td>
                                    <td>
                                        {{ issue.updated_at | date }}
                                    </td>
                                </tr>
                            </tbody>
                        </nz-table>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>
